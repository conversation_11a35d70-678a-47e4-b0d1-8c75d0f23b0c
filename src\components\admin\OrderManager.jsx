import { useState, useEffect } from "react";
import { httpsCallable } from "firebase/functions";
import { functions } from "../../firebase/config";
import styled from "styled-components";
import { colors, spacing, typography, breakpoints } from "../../styles";
import { useTheme } from "../../context/ThemeContext";
import { useAuth } from "../../context/AuthContext";
import { FaSearch, FaFilter, FaSort, FaDownload, FaEye, FaEdit, FaTrash } from "react-icons/fa";
import DeleteConfirmationModal from "../ui/DeleteConfirmationModal";
import { formatCurrency, formatDate } from "../../utils/formatters";
import { jsPDF } from "jspdf";
import "jspdf-autotable";

// Styled components
const Container = styled.div`
  padding: ${spacing.lg};
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.darker : colors.neutral.white};
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${spacing.md};
  flex-wrap: wrap;
  gap: ${spacing.sm};

  @media (max-width: ${breakpoints.md}) {
    flex-direction: column;
    align-items: flex-start;
  }
`;

const Title = styled.h2`
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};
  margin: 0;
`;

const Controls = styled.div`
  display: flex;
  gap: ${spacing.sm};
  flex-wrap: wrap;

  @media (max-width: ${breakpoints.sm}) {
    width: 100%;
    justify-content: space-between;
  }
`;

const SearchContainer = styled.div`
  position: relative;
  margin-bottom: ${spacing.md};
`;

const SearchInput = styled.input`
  width: 100%;
  padding: ${spacing.sm} ${spacing.sm} ${spacing.sm} ${spacing.xl};
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
  border-radius: 4px;
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};

  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
  }
`;

const SearchIcon = styled.div`
  position: absolute;
  left: ${spacing.sm};
  top: 50%;
  transform: translateY(-50%);
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.gray};
`;

const FiltersContainer = styled.div`
  display: flex;
  gap: ${spacing.md};
  margin-bottom: ${spacing.md};
  flex-wrap: wrap;

  @media (max-width: ${breakpoints.sm}) {
    flex-direction: column;
  }
`;

const FilterGroup = styled.div`
  display: flex;
  flex-direction: column;
  min-width: 150px;
`;

const FilterLabel = styled.label`
  font-size: ${typography.fontSize.sm};
  margin-bottom: ${spacing.xs};
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
`;

const Select = styled.select`
  padding: ${spacing.xs} ${spacing.sm};
  border: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
  border-radius: 4px;
  background-color: ${(props) =>
    props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};

  &:focus {
    outline: none;
    border-color: ${colors.primary.main};
  }
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: ${spacing.xs};
  padding: ${spacing.xs} ${spacing.sm};
  background-color: ${(props) =>
    props.primary ? colors.primary.main : props.darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${(props) =>
    props.primary ? colors.neutral.white : props.darkMode ? colors.neutral.white : colors.neutral.dark};
  border: 1px solid
    ${(props) =>
      props.primary ? colors.primary.main : props.darkMode ? colors.neutral.dark : colors.neutral.light};
  border-radius: 4px;
  cursor: pointer;
  font-size: ${typography.fontSize.sm};

  &:hover {
    background-color: ${(props) =>
      props.primary ? colors.primary.dark : props.darkMode ? colors.neutral.darker : colors.neutral.lighter};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-bottom: ${spacing.lg};
`;

const Th = styled.th`
  text-align: left;
  padding: ${spacing.sm};
  border-bottom: 2px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};
  font-weight: ${typography.fontWeight.medium};
`;

const Td = styled.td`
  padding: ${spacing.sm};
  border-bottom: 1px solid
    ${(props) => (props.darkMode ? colors.neutral.dark : colors.neutral.light)};
  color: ${(props) =>
    props.darkMode ? colors.neutral.white : colors.neutral.dark};
`;

const StatusBadge = styled.span`
  display: inline-block;
  padding: ${spacing.xs} ${spacing.sm};
  border-radius: 12px;
  font-size: ${typography.fontSize.xs};
  font-weight: ${typography.fontWeight.medium};
  text-transform: uppercase;
  background-color: ${(props) => {
    switch (props.status) {
      case "processing":
        return colors.info.light;
      case "shipped":
        return colors.warning.light;
      case "delivered":
        return colors.success.light;
      case "cancelled":
        return colors.error.light;
      case "refunded":
        return colors.neutral.light;
      default:
        return colors.neutral.light;
    }
  }};
  color: ${(props) => {
    switch (props.status) {
      case "processing":
        return colors.info.dark;
      case "shipped":
        return colors.warning.dark;
      case "delivered":
        return colors.success.dark;
      case "cancelled":
        return colors.error.dark;
      case "refunded":
        return colors.neutral.dark;
      default:
        return colors.neutral.dark;
    }
  }};
`;

const ActionButton = styled(Button)`
  padding: ${spacing.xs};
  margin-right: ${spacing.xs};
`;

const Pagination = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: ${spacing.md};
`;

const PageInfo = styled.div`
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.dark};
  font-size: ${typography.fontSize.sm};
`;

const PageButtons = styled.div`
  display: flex;
  gap: ${spacing.xs};
`;

const PageButton = styled.button`
  padding: ${spacing.xs} ${spacing.sm};
  background-color: ${(props) =>
    props.active
      ? colors.primary.main
      : props.darkMode
      ? colors.neutral.dark
      : colors.neutral.white};
  color: ${(props) =>
    props.active
      ? colors.neutral.white
      : props.darkMode
      ? colors.neutral.white
      : colors.neutral.dark};
  border: 1px solid
    ${(props) =>
      props.active
        ? colors.primary.main
        : props.darkMode
        ? colors.neutral.dark
        : colors.neutral.light};
  border-radius: 4px;
  cursor: ${(props) => (props.disabled ? "not-allowed" : "pointer")};
  opacity: ${(props) => (props.disabled ? 0.5 : 1)};

  &:hover:not(:disabled) {
    background-color: ${(props) =>
      props.active
        ? colors.primary.dark
        : props.darkMode
        ? colors.neutral.darker
        : colors.neutral.lighter};
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${spacing.xl};
  color: ${(props) =>
    props.darkMode ? colors.neutral.light : colors.neutral.gray};
`;

const OrderManager = () => {
  const { darkMode } = useTheme();
  const { currentUser, isAdmin, isEditor } = useAuth();
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalOrders, setTotalOrders] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [lastVisible, setLastVisible] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState({
    status: "all",
    dateRange: null,
    minTotal: null,
    maxTotal: null,
  });
  const [sortBy, setSortBy] = useState("date");
  const [sortDirection, setSortDirection] = useState("desc");
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [statusUpdateLoading, setStatusUpdateLoading] = useState(false);

  const ordersPerPage = 10;

  // Function to fetch orders
  const fetchOrders = async () => {
    setLoading(true);
    setError(null);

    try {
      const getAllOrders = httpsCallable(functions, "getAllOrders");
      const result = await getAllOrders({
        page: currentPage,
        limit: ordersPerPage,
        startAfter: lastVisible,
        sortBy,
        sortDirection,
        filters: {
          ...filters,
          search: searchQuery,
        },
      });

      const { orders: fetchedOrders, hasMore, total, lastVisible: newLastVisible } = result.data;
      
      setOrders(fetchedOrders);
      setHasMore(hasMore);
      setTotalOrders(total);
      setLastVisible(newLastVisible);
    } catch (err) {
      console.error("Error fetching orders:", err);
      setError("Failed to load orders. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    if (currentUser && (isAdmin || isEditor)) {
      fetchOrders();
    }
  }, [currentUser, isAdmin, isEditor, currentPage, sortBy, sortDirection]);

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters((prev) => ({ ...prev, [name]: value }));
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Handle search
  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  // Apply filters
  const applyFilters = () => {
    setCurrentPage(1);
    fetchOrders();
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      status: "all",
      dateRange: null,
      minTotal: null,
      maxTotal: null,
    });
    setSearchQuery("");
    setCurrentPage(1);
    fetchOrders();
  };

  // Handle sort
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortDirection("desc");
    }
    setCurrentPage(1);
  };

  // Handle status update
  const updateOrderStatus = async (orderId, newStatus) => {
    setStatusUpdateLoading(true);
    try {
      const updateStatus = httpsCallable(functions, "updateOrderStatus");
      await updateStatus({ orderId, status: newStatus });
      
      // Update local state
      setOrders((prevOrders) =>
        prevOrders.map((order) =>
          order.id === orderId ? { ...order, status: newStatus } : order
        )
      );
    } catch (err) {
      console.error("Error updating order status:", err);
      setError(`Failed to update order status: ${err.message}`);
    } finally {
      setStatusUpdateLoading(false);
    }
  };

  // Handle delete
  const handleDelete = (order) => {
    setSelectedOrder(order);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    try {
      // For now, we'll just cancel the order instead of deleting it
      await updateOrderStatus(selectedOrder.id, "cancelled");
      setShowDeleteModal(false);
      setSelectedOrder(null);
    } catch (err) {
      console.error("Error cancelling order:", err);
      setError(`Failed to cancel order: ${err.message}`);
    }
  };

  // Export orders to PDF
  const exportToPDF = () => {
    const doc = new jsPDF();
    
    // Add title
    doc.setFontSize(18);
    doc.text("Orders Report", 14, 22);
    
    // Add date
    doc.setFontSize(11);
    doc.text(`Generated: ${new Date().toLocaleDateString()}`, 14, 30);
    
    // Create table
    const tableColumn = ["Order ID", "Date", "Customer", "Status", "Total"];
    const tableRows = orders.map((order) => [
      order.id,
      formatDate(order.createdAt),
      order.userEmail,
      order.status,
      formatCurrency(order.total),
    ]);
    
    doc.autoTable({
      head: [tableColumn],
      body: tableRows,
      startY: 40,
      theme: "grid",
      styles: { fontSize: 9 },
      headStyles: { fillColor: [66, 66, 66] },
    });
    
    doc.save("orders-report.pdf");
  };

  return (
    <Container darkMode={darkMode}>
      <Header>
        <Title darkMode={darkMode}>Order Management</Title>
        <Controls>
          <Button darkMode={darkMode} onClick={exportToPDF}>
            <FaDownload /> Export
          </Button>
        </Controls>
      </Header>

      <SearchContainer>
        <SearchIcon darkMode={darkMode}>
          <FaSearch />
        </SearchIcon>
        <SearchInput
          type="text"
          placeholder="Search by order ID, customer name or email..."
          value={searchQuery}
          onChange={handleSearch}
          darkMode={darkMode}
        />
      </SearchContainer>

      <FiltersContainer>
        <FilterGroup>
          <FilterLabel darkMode={darkMode}>Status</FilterLabel>
          <Select
            name="status"
            value={filters.status}
            onChange={handleFilterChange}
            darkMode={darkMode}
          >
            <option value="all">All Statuses</option>
            <option value="processing">Processing</option>
            <option value="shipped">Shipped</option>
            <option value="delivered">Delivered</option>
            <option value="cancelled">Cancelled</option>
            <option value="refunded">Refunded</option>
          </Select>
        </FilterGroup>

        <FilterGroup>
          <FilterLabel darkMode={darkMode}>Sort By</FilterLabel>
          <Select
            name="sortBy"
            value={sortBy}
            onChange={(e) => {
              setSortBy(e.target.value);
              setCurrentPage(1);
            }}
            darkMode={darkMode}
          >
            <option value="date">Date</option>
            <option value="total">Total</option>
          </Select>
        </FilterGroup>

        <FilterGroup>
          <FilterLabel darkMode={darkMode}>Direction</FilterLabel>
          <Select
            name="sortDirection"
            value={sortDirection}
            onChange={(e) => {
              setSortDirection(e.target.value);
              setCurrentPage(1);
            }}
            darkMode={darkMode}
          >
            <option value="desc">Descending</option>
            <option value="asc">Ascending</option>
          </Select>
        </FilterGroup>

        <div style={{ display: "flex", alignItems: "flex-end" }}>
          <Button darkMode={darkMode} onClick={applyFilters}>
            <FaFilter /> Apply Filters
          </Button>
          <Button
            darkMode={darkMode}
            onClick={resetFilters}
            style={{ marginLeft: spacing.xs }}
          >
            Reset
          </Button>
        </div>
      </FiltersContainer>

      {loading ? (
        <EmptyState darkMode={darkMode}>Loading orders...</EmptyState>
      ) : error ? (
        <EmptyState darkMode={darkMode}>{error}</EmptyState>
      ) : orders.length === 0 ? (
        <EmptyState darkMode={darkMode}>No orders found</EmptyState>
      ) : (
        <>
          <div style={{ overflowX: "auto" }}>
            <Table>
              <thead>
                <tr>
                  <Th darkMode={darkMode}>
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        cursor: "pointer",
                      }}
                      onClick={() => handleSort("date")}
                    >
                      Order ID / Date
                      {sortBy === "date" && (
                        <FaSort
                          style={{
                            marginLeft: spacing.xs,
                            transform:
                              sortDirection === "asc"
                                ? "rotate(180deg)"
                                : "none",
                          }}
                        />
                      )}
                    </div>
                  </Th>
                  <Th darkMode={darkMode}>Customer</Th>
                  <Th darkMode={darkMode}>Items</Th>
                  <Th darkMode={darkMode}>Status</Th>
                  <Th darkMode={darkMode}>
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        cursor: "pointer",
                      }}
                      onClick={() => handleSort("total")}
                    >
                      Total
                      {sortBy === "total" && (
                        <FaSort
                          style={{
                            marginLeft: spacing.xs,
                            transform:
                              sortDirection === "asc"
                                ? "rotate(180deg)"
                                : "none",
                          }}
                        />
                      )}
                    </div>
                  </Th>
                  <Th darkMode={darkMode}>Actions</Th>
                </tr>
              </thead>
              <tbody>
                {orders.map((order) => (
                  <tr key={order.id}>
                    <Td darkMode={darkMode}>
                      <div>
                        <div>{order.id}</div>
                        <div style={{ fontSize: typography.fontSize.sm, color: darkMode ? colors.neutral.light : colors.neutral.gray }}>
                          {formatDate(order.createdAt)}
                        </div>
                      </div>
                    </Td>
                    <Td darkMode={darkMode}>
                      <div>
                        <div>{order.shipping?.name}</div>
                        <div style={{ fontSize: typography.fontSize.sm, color: darkMode ? colors.neutral.light : colors.neutral.gray }}>
                          {order.userEmail}
                        </div>
                      </div>
                    </Td>
                    <Td darkMode={darkMode}>
                      {order.items?.length || 0} items
                    </Td>
                    <Td darkMode={darkMode}>
                      <StatusBadge status={order.status}>
                        {order.status}
                      </StatusBadge>
                    </Td>
                    <Td darkMode={darkMode}>{formatCurrency(order.total)}</Td>
                    <Td darkMode={darkMode}>
                      <div style={{ display: "flex" }}>
                        <ActionButton
                          darkMode={darkMode}
                          title="View Details"
                        >
                          <FaEye />
                        </ActionButton>
                        <ActionButton
                          darkMode={darkMode}
                          title="Update Status"
                          disabled={statusUpdateLoading}
                        >
                          <FaEdit />
                        </ActionButton>
                        <ActionButton
                          darkMode={darkMode}
                          title="Cancel Order"
                          onClick={() => handleDelete(order)}
                          disabled={
                            order.status === "cancelled" ||
                            order.status === "refunded" ||
                            order.status === "delivered"
                          }
                        >
                          <FaTrash />
                        </ActionButton>
                      </div>
                    </Td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </div>

          <Pagination>
            <PageInfo darkMode={darkMode}>
              Showing {(currentPage - 1) * ordersPerPage + 1} to{" "}
              {Math.min(currentPage * ordersPerPage, totalOrders)} of{" "}
              {totalOrders} orders
            </PageInfo>
            <PageButtons>
              <PageButton
                darkMode={darkMode}
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
              >
                First
              </PageButton>
              <PageButton
                darkMode={darkMode}
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                Previous
              </PageButton>
              <PageButton darkMode={darkMode} active>
                {currentPage}
              </PageButton>
              <PageButton
                darkMode={darkMode}
                onClick={() => setCurrentPage((prev) => prev + 1)}
                disabled={!hasMore}
              >
                Next
              </PageButton>
            </PageButtons>
          </Pagination>
        </>
      )}

      <DeleteConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false);
          setSelectedOrder(null);
        }}
        onConfirm={confirmDelete}
        itemName={`order ${selectedOrder?.id}`}
        darkMode={darkMode}
      />
    </Container>
  );
};

export default OrderManager;
