import { storage } from '../firebase/config';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';

/**
 * Uploads an image file to Firebase Storage
 * @param {File} imageFile - The file to upload
 * @param {string} folder - The folder in storage (products, blog, profiles, etc.)
 * @returns {Promise<string>} - The download URL of the uploaded image
 */
export const uploadImage = async (imageFile, folder = 'products') => {
  let imageUrl = '';
  
  if (!imageFile) return imageUrl;
  
  try {
    // First try using the Cloud Function (this should work with CORS)
    const uploadData = new FormData();
    uploadData.append('file', imageFile);
    uploadData.append('folder', folder);
    
    try {
      const uploadResponse = await fetch('https://us-central1-skinglow1000.cloudfunctions.net/uploadFile', {
        method: 'POST',
        mode: 'cors',
        credentials: 'same-origin',
        headers: {
          'Accept': 'application/json',
        },
        body: uploadData
      });
      
      if (uploadResponse.ok) {
        const result = await uploadResponse.json();
        if (result.fileUrl) {
          return result.fileUrl;
        }
      }
      
      // If cloud function fails, fall back to client-side upload
      console.log('Falling back to client-side upload...');
    } catch (cloudError) {
      console.error('Cloud function upload failed, falling back to client-side:', cloudError);
    }
    
    // Client-side upload fallback
    const storageRef = ref(storage, `${folder}/${Date.now()}_${imageFile.name}`);
    const snapshot = await uploadBytes(storageRef, imageFile);
    imageUrl = await getDownloadURL(snapshot.ref);
    
    return imageUrl;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
};

