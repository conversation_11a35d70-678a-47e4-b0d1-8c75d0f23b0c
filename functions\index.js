const admin = require("firebase-admin");
const functions = require("firebase-functions");

// Initialize admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

// Initialize Stripe with the key from Firebase config
let stripeInstance;
try {
  // Try to get the key from Firebase config first
  const stripeKey =
    functions.config().stripe?.secret || process.env.STRIPE_SECRET_KEY;

  if (!stripeKey) {
    console.warn(
      "Stripe secret key not found in config or environment variables"
    );
    throw new Error("Stripe secret key not found");
  }

  console.log(
    "Initializing Stripe with key:",
    stripeKey ? "Key found (hidden for security)" : "No key found"
  );
  stripeInstance = require("stripe")(stripeKey);
  console.log("Stripe initialized successfully");
} catch (error) {
  console.error("Error initializing Stripe:", error);

  // For development/testing only - use the hardcoded key from .env file
  // In production, this should be set through Firebase config
  const hardcodedKey =
    "sk_test_51R62oRCMkLTqpmgQ8onl6m8RqRBP3BNMfvdXjBUcN9zYINn8UXeUEOIaSf1YYcrAxxVtZrzTJ7ktEYLdrvj83opZ00bGuzNYiV";
  console.log("Attempting to initialize Stripe with hardcoded key");

  try {
    stripeInstance = require("stripe")(hardcodedKey);
    console.log("Stripe initialized with hardcoded key");
  } catch (fallbackError) {
    console.error(
      "Failed to initialize Stripe with hardcoded key:",
      fallbackError
    );

    // Initialize with a placeholder to prevent crashes
    stripeInstance = {
      paymentIntents: { create: () => ({ client_secret: "test_secret" }) },
      customers: {
        create: () => ({}),
        list: () => ({ data: [] }),
        update: () => ({}),
      },
      paymentMethods: { list: () => ({ data: [] }) },
      refunds: { create: () => ({}) },
    };
    console.warn("Using placeholder Stripe instance - payments will not work");
  }
}

const { Storage } = require("@google-cloud/storage");
const Busboy = require("busboy");
const path = require("path");
const os = require("os");
const fs = require("fs");

// Only create the storage instance when needed
let _storage = null;
const getStorage = () => {
  if (!_storage) {
    _storage = new Storage();
  }
  return _storage;
};

// Add a utility function for error handling
const handleError = (error, message) => {
  console.error(message, error);
  return { error: error.message };
};

// Add admin role function (using callable function)
exports.addAdminRole = functions.https.onCall(async (data, context) => {
  // Check if the request is made by an admin
  if (context.auth.token.admin !== true) {
    return {
      error: "Only admins can add other admins",
    };
  }

  // Validate email
  if (!data.email || typeof data.email !== "string") {
    return {
      error: "Please provide a valid email address",
    };
  }

  try {
    // Get user by email and add custom claim
    const user = await admin.auth().getUserByEmail(data.email);
    await admin.auth().setCustomUserClaims(user.uid, {
      admin: true,
    });

    return {
      message: `Success! ${data.email} has been made an admin.`,
    };
  } catch (err) {
    console.error("Error adding admin role:", err);

    // Return specific error messages
    if (err.code === "auth/user-not-found") {
      return {
        error: `User with email ${data.email} does not exist.`,
      };
    }

    return {
      error: `Error: ${err.message}`,
    };
  }
});

// Function to initialize the first admin (for bootstrapping)
exports.initializeAdminCallable = functions.https.onCall(
  async (data, context) => {
    try {
      // Check if the user is authenticated
      if (!context.auth) {
        throw new functions.https.HttpsError(
          "unauthenticated",
          "User must be logged in"
        );
      }

      // Get the current user's email
      const adminEmail = context.auth.token.email;

      // Get user by email
      const userRecord = await admin.auth().getUserByEmail(adminEmail);

      // Set admin claim
      await admin.auth().setCustomUserClaims(userRecord.uid, { admin: true });

      console.log(`Admin role set for ${adminEmail}`);
      return {
        success: true,
        message: `Admin role set for ${adminEmail}`,
      };
    } catch (error) {
      console.error("Error initializing admin:", error);
      throw new functions.https.HttpsError("internal", error.message);
    }
  }
);

// Define CORS options
const corsOptions = {
  origin: true, // Allow requests from any origin
  methods: ["POST", "GET", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization"],
  credentials: true,
  maxAge: 86400, // 24 hours
};

// Initialize CORS middleware with options
const cors = require("cors")(corsOptions);

// File upload function - optimize to avoid timeout
exports.uploadFile = functions.https.onRequest((req, res) => {
  // Handle preflight OPTIONS request
  if (req.method === "OPTIONS") {
    res.set("Access-Control-Allow-Origin", "*");
    res.set("Access-Control-Allow-Methods", "POST, GET, OPTIONS");
    res.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
    res.set("Access-Control-Max-Age", "86400");
    res.status(204).send("");
    return;
  }

  cors(req, res, async () => {
    if (req.method !== "POST") {
      return res.status(405).json({ error: "Method Not Allowed" });
    }

    try {
      const busboy = Busboy({ headers: req.headers });
      const uploads = {};
      const fields = {};

      busboy.on("file", (fieldname, file, info) => {
        const { filename, mimeType } = info;
        console.log(`Processing file: ${filename}, mimetype: ${mimeType}`);

        const filepath = path.join(os.tmpdir(), filename);
        uploads[fieldname] = { file: file, filepath, mimeType };

        const writeStream = fs.createWriteStream(filepath);
        file.pipe(writeStream);
      });

      busboy.on("field", (fieldname, val) => {
        fields[fieldname] = val;
      });

      // Process when complete
      busboy.on("finish", async () => {
        const folder = fields.folder || "uploads";
        const file = uploads.file;

        if (!file) {
          return res.status(400).json({ error: "No file uploaded" });
        }

        const bucket = getStorage().bucket("skinglow1000.appspot.com");
        const destination = `${folder}/${Date.now()}_${path.basename(
          file.filepath
        )}`;

        await bucket.upload(file.filepath, {
          destination,
          metadata: {
            contentType: file.mimeType,
            metadata: {
              firebaseStorageDownloadTokens: admin.database().ref().push().key,
            },
          },
        });

        const fileUrl = `https://firebasestorage.googleapis.com/v0/b/skinglow1000.appspot.com/o/${encodeURIComponent(
          destination
        )}?alt=media`;

        // Clean up temp file
        fs.unlinkSync(file.filepath);

        return res.status(200).json({ success: true, fileUrl });
      });

      busboy.end(req.rawBody);
    } catch (error) {
      return res.status(500).json(handleError(error, "Error uploading file"));
    }
  });
});

// Add a simple health check function
exports.healthCheck = functions.https.onRequest((req, res) => {
  res.status(200).send("Firebase Functions are running");
});

// Create a payment intent with Stripe
exports.createPaymentIntent = functions.https.onRequest((req, res) => {
  cors(req, res, async () => {
    try {
      if (req.method !== "POST") {
        return res.status(405).json({ error: "Method Not Allowed" });
      }

      const {
        amount,
        currency = "usd",
        customer_email,
        payment_method_id,
        save_payment_method,
      } = req.body;

      if (!amount || amount <= 0) {
        return res.status(400).json({ error: "Valid amount is required" });
      }

      // Create or retrieve a customer
      let customer;
      if (customer_email) {
        // Check if customer already exists
        const customers = await stripeInstance.customers.list({
          email: customer_email,
          limit: 1,
        });

        if (customers.data.length > 0) {
          customer = customers.data[0];
        } else {
          // Create a new customer
          customer = await stripeInstance.customers.create({
            email: customer_email,
          });
        }
      }

      // Create payment intent options
      const paymentIntentOptions = {
        amount: Math.round(amount), // Stripe requires integer amount in cents
        currency: currency,
        customer: customer ? customer.id : undefined,
        metadata: {
          customer_email: customer_email,
        },
      };

      // If using a saved payment method
      if (payment_method_id) {
        paymentIntentOptions.payment_method = payment_method_id;
        paymentIntentOptions.confirm = true;
        paymentIntentOptions.setup_future_usage = save_payment_method
          ? "off_session"
          : undefined;
      } else {
        // For new payment methods
        paymentIntentOptions.automatic_payment_methods = {
          enabled: true,
        };

        if (save_payment_method && customer) {
          paymentIntentOptions.setup_future_usage = "off_session";
        }
      }

      // Create a payment intent
      const paymentIntent = await stripeInstance.paymentIntents.create(
        paymentIntentOptions
      );

      // Return the client secret
      return res.status(200).json({
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        customerId: customer ? customer.id : null,
      });
    } catch (error) {
      console.error("Error creating payment intent:", error);
      return res
        .status(500)
        .json(handleError(error, "Error creating payment intent"));
    }
  });
});

// Get customer payment methods
exports.getCustomerPaymentMethods = functions.https.onRequest((req, res) => {
  cors(req, res, async () => {
    try {
      if (req.method !== "GET") {
        return res.status(405).json({ error: "Method Not Allowed" });
      }

      const { customer_email } = req.query;

      if (!customer_email) {
        return res.status(400).json({ error: "Customer email is required" });
      }

      // Find customer by email
      const customers = await stripeInstance.customers.list({
        email: customer_email,
        limit: 1,
      });

      if (customers.data.length === 0) {
        return res.status(404).json({ error: "Customer not found" });
      }

      const customer = customers.data[0];

      // Get payment methods
      const paymentMethods = await stripeInstance.paymentMethods.list({
        customer: customer.id,
        type: "card",
      });

      // Format payment methods for client
      const formattedPaymentMethods = paymentMethods.data.map((pm) => ({
        id: pm.id,
        brand: pm.card.brand,
        last4: pm.card.last4,
        expMonth: pm.card.exp_month,
        expYear: pm.card.exp_year,
        isDefault: pm.id === customer.invoice_settings?.default_payment_method,
      }));

      return res.status(200).json({ paymentMethods: formattedPaymentMethods });
    } catch (error) {
      console.error("Error getting payment methods:", error);
      return res
        .status(500)
        .json(handleError(error, "Error getting payment methods"));
    }
  });
});

// Set default payment method
exports.setDefaultPaymentMethod = functions.https.onRequest((req, res) => {
  cors(req, res, async () => {
    try {
      if (req.method !== "POST") {
        return res.status(405).json({ error: "Method Not Allowed" });
      }

      const { customer_email, payment_method_id } = req.body;

      if (!customer_email || !payment_method_id) {
        return res
          .status(400)
          .json({ error: "Customer email and payment method ID are required" });
      }

      // Find customer by email
      const customers = await stripeInstance.customers.list({
        email: customer_email,
        limit: 1,
      });

      if (customers.data.length === 0) {
        return res.status(404).json({ error: "Customer not found" });
      }

      const customer = customers.data[0];

      // Update customer's default payment method
      await stripeInstance.customers.update(customer.id, {
        invoice_settings: {
          default_payment_method: payment_method_id,
        },
      });

      return res.status(200).json({ success: true });
    } catch (error) {
      console.error("Error setting default payment method:", error);
      return res
        .status(500)
        .json(handleError(error, "Error setting default payment method"));
    }
  });
});

// Update order status
exports.updateOrderStatus = functions.https.onCall(async (data, context) => {
  try {
    // Check if the request is made by an admin or editor
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "You must be logged in to perform this action"
      );
    }

    if (!context.auth.token.admin && !context.auth.token.editor) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "Only admins or editors can update order status"
      );
    }

    const { orderId, status } = data;

    if (!orderId || !status) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Order ID and status are required"
      );
    }

    // Valid order statuses
    const validStatuses = [
      "processing",
      "shipped",
      "delivered",
      "cancelled",
      "refunded",
    ];

    if (!validStatuses.includes(status)) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        `Status must be one of: ${validStatuses.join(", ")}`
      );
    }

    // Update the order in Firestore
    const orderRef = admin.firestore().collection("orders").doc(orderId);
    const orderDoc = await orderRef.get();

    if (!orderDoc.exists) {
      throw new functions.https.HttpsError("not-found", "Order not found");
    }

    await orderRef.update({
      status: status,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    // If order is cancelled or refunded and there's a Stripe payment, handle refund
    const orderData = orderDoc.data();
    if (
      (status === "cancelled" || status === "refunded") &&
      orderData.payment &&
      orderData.payment.method === "stripe" &&
      orderData.payment.transactionId
    ) {
      try {
        // Create a refund in Stripe
        await stripeInstance.refunds.create({
          payment_intent: orderData.payment.transactionId,
          reason:
            status === "cancelled" ? "requested_by_customer" : "duplicate",
        });
      } catch (refundError) {
        console.error("Error processing refund:", refundError);
        // Continue with order status update even if refund fails
      }
    }

    return { success: true, message: `Order status updated to ${status}` };
  } catch (error) {
    console.error("Error updating order status:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// Get user orders
exports.getUserOrders = functions.https.onCall(async (data, context) => {
  try {
    // Check if the user is authenticated
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "You must be logged in to view your orders"
      );
    }

    const userId = context.auth.uid;

    // Query orders for this user
    const ordersRef = admin.firestore().collection("orders");
    let query = ordersRef.where("userId", "==", userId);

    // Add sorting if provided
    if (data.sortBy) {
      const sortField = data.sortBy === "date" ? "createdAt" : "total";
      const sortDirection = data.sortDirection === "asc" ? "asc" : "desc";
      query = query.orderBy(sortField, sortDirection);
    } else {
      // Default sort by date descending
      query = query.orderBy("createdAt", "desc");
    }

    const snapshot = await query.get();

    // Format orders for response
    const orders = snapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: data.createdAt
          ? data.createdAt.toDate().toISOString()
          : null,
        updatedAt: data.updatedAt
          ? data.updatedAt.toDate().toISOString()
          : null,
      };
    });

    return { orders };
  } catch (error) {
    console.error("Error getting user orders:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// Cancel order (for users)
exports.cancelOrder = functions.https.onCall(async (data, context) => {
  try {
    // Check if the user is authenticated
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "You must be logged in to cancel an order"
      );
    }

    const userId = context.auth.uid;
    const { orderId } = data;

    if (!orderId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Order ID is required"
      );
    }

    // Get the order
    const orderRef = admin.firestore().collection("orders").doc(orderId);
    const orderDoc = await orderRef.get();

    if (!orderDoc.exists) {
      throw new functions.https.HttpsError("not-found", "Order not found");
    }

    const orderData = orderDoc.data();

    // Check if this order belongs to the user
    if (orderData.userId !== userId) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "You can only cancel your own orders"
      );
    }

    // Check if the order can be cancelled
    if (
      orderData.status === "delivered" ||
      orderData.status === "cancelled" ||
      orderData.status === "refunded"
    ) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        `Cannot cancel order with status: ${orderData.status}`
      );
    }

    // Update order status
    await orderRef.update({
      status: "cancelled",
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    // If there's a Stripe payment, handle refund
    if (
      orderData.payment &&
      orderData.payment.method === "stripe" &&
      orderData.payment.transactionId
    ) {
      try {
        // Create a refund in Stripe
        await stripeInstance.refunds.create({
          payment_intent: orderData.payment.transactionId,
          reason: "requested_by_customer",
        });
      } catch (refundError) {
        console.error("Error processing refund:", refundError);
        // Continue with order cancellation even if refund fails
      }
    }

    return { success: true, message: "Order cancelled successfully" };
  } catch (error) {
    console.error("Error cancelling order:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// Get all orders (for admin)
exports.getAllOrders = functions.https.onCall(async (data, context) => {
  try {
    // Check if the request is made by an admin or editor
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "You must be logged in to perform this action"
      );
    }

    if (!context.auth.token.admin && !context.auth.token.editor) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "Only admins or editors can view all orders"
      );
    }

    // Parse pagination parameters
    const limit = data.limit || 20;
    const page = data.page || 1;
    const startAfter = data.startAfter || null;

    // Parse filter parameters
    const filters = data.filters || {};
    const { status, dateRange, minTotal, maxTotal, search } = filters;

    // Build query
    const ordersRef = admin.firestore().collection("orders");
    let query = ordersRef;

    // Apply filters
    if (status && status !== "all") {
      query = query.where("status", "==", status);
    }

    if (dateRange && dateRange.start) {
      const startDate = new Date(dateRange.start);
      query = query.where("createdAt", ">=", startDate);
    }

    if (dateRange && dateRange.end) {
      const endDate = new Date(dateRange.end);
      query = query.where("createdAt", "<=", endDate);
    }

    // Add sorting
    const sortField = data.sortBy === "total" ? "total" : "createdAt";
    const sortDirection = data.sortDirection === "asc" ? "asc" : "desc";
    query = query.orderBy(sortField, sortDirection);

    // Apply pagination
    if (startAfter) {
      const startAfterDoc = await admin
        .firestore()
        .collection("orders")
        .doc(startAfter)
        .get();
      if (startAfterDoc.exists) {
        query = query.startAfter(startAfterDoc);
      }
    } else if (page > 1) {
      // Skip pages if startAfter is not provided
      query = query.limit((page - 1) * limit);
      const skipSnapshot = await query.get();
      const lastVisible = skipSnapshot.docs[skipSnapshot.docs.length - 1];
      if (lastVisible) {
        query = ordersRef
          .orderBy(sortField, sortDirection)
          .startAfter(lastVisible);
      } else {
        // If we can't skip ahead, return empty results
        return { orders: [], hasMore: false, total: 0 };
      }
    }

    // Apply limit
    query = query.limit(limit);

    // Execute query
    const snapshot = await query.get();

    // Get total count (for pagination)
    const countSnapshot = await ordersRef.count().get();
    const total = countSnapshot.data().count;

    // Format orders for response
    let orders = snapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: data.createdAt
          ? data.createdAt.toDate().toISOString()
          : null,
        updatedAt: data.updatedAt
          ? data.updatedAt.toDate().toISOString()
          : null,
      };
    });

    // Apply client-side filters that can't be done in Firestore query
    if (minTotal !== undefined) {
      orders = orders.filter((order) => order.total >= minTotal);
    }

    if (maxTotal !== undefined) {
      orders = orders.filter((order) => order.total <= maxTotal);
    }

    if (search) {
      const searchLower = search.toLowerCase();
      orders = orders.filter(
        (order) =>
          (order.id && order.id.toLowerCase().includes(searchLower)) ||
          (order.shipping &&
            order.shipping.name &&
            order.shipping.name.toLowerCase().includes(searchLower)) ||
          (order.userEmail &&
            order.userEmail.toLowerCase().includes(searchLower))
      );
    }

    // Check if there are more results
    const hasMore = snapshot.docs.length === limit;

    return {
      orders,
      hasMore,
      total,
      lastVisible:
        snapshot.docs.length > 0
          ? snapshot.docs[snapshot.docs.length - 1].id
          : null,
    };
  } catch (error) {
    console.error("Error getting all orders:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// Get admin users function
exports.getAdminUsers = functions.https.onCall(async (data, context) => {
  // Check if the request is made by an admin
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "You must be logged in to perform this action"
    );
  }

  if (context.auth.token.admin !== true) {
    throw new functions.https.HttpsError(
      "permission-denied",
      "Only admins can view the admin list"
    );
  }

  // Log for debugging
  console.log("User requesting admin list:", context.auth.uid);
  console.log("User token:", context.auth.token);

  try {
    // List all users with admin claim
    const listUsersResult = await admin.auth().listUsers();
    const admins = [];

    listUsersResult.users.forEach((userRecord) => {
      const claims = userRecord.customClaims || {};
      if (claims.admin) {
        admins.push({
          email: userRecord.email,
          uid: userRecord.uid,
          displayName: userRecord.displayName,
        });
      }
    });

    return { admins };
  } catch (error) {
    console.error("Error fetching admin users:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// Remove admin role
exports.removeAdminRole = functions.https.onCall(async (data, context) => {
  // Check if request is made by an admin
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "You must be logged in to perform this action"
    );
  }

  if (!context.auth.token.admin) {
    throw new functions.https.HttpsError(
      "permission-denied",
      "Only administrators can remove admin privileges"
    );
  }

  // Get the email from the request
  const { email } = data;

  if (!email) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Email is required"
    );
  }

  try {
    // Get the user by email
    const user = await admin.auth().getUserByEmail(email);

    // Check if trying to remove own admin privileges
    if (user.email === context.auth.token.email) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "You cannot remove your own admin privileges"
      );
    }

    // Remove admin claim by setting it to false
    const customClaims = user.customClaims || {};
    customClaims.admin = false;

    await admin.auth().setCustomUserClaims(user.uid, customClaims);

    return {
      message: `Successfully removed admin privileges from ${email}`,
    };
  } catch (error) {
    console.error("Error removing admin role:", error);
    throw new functions.https.HttpsError("internal", error.message);
  }
});

// Add editor role function
exports.addEditorRole = functions.https.onCall(async (data, context) => {
  // Check if the request is made by an admin
  if (context.auth.token.admin !== true) {
    return {
      error: "Only admins can add editors",
    };
  }

  // Validate email
  if (!data.email || typeof data.email !== "string") {
    return {
      error: "Please provide a valid email address",
    };
  }

  try {
    // Get user by email and add custom claim
    const user = await admin.auth().getUserByEmail(data.email);

    // Get existing claims
    const customClaims = user.customClaims || {};

    // Add editor role
    customClaims.editor = true;

    await admin.auth().setCustomUserClaims(user.uid, customClaims);

    return {
      message: `Success! ${data.email} has been made an editor.`,
    };
  } catch (err) {
    console.error("Error adding editor role:", err);

    // Return specific error messages
    if (err.code === "auth/user-not-found") {
      return {
        error: `User with email ${data.email} does not exist.`,
      };
    }

    return {
      error: `Error: ${err.message}`,
    };
  }
});

// Remove editor role function
exports.removeEditorRole = functions.https.onCall(async (data, context) => {
  // Check if the request is made by an admin
  if (context.auth.token.admin !== true) {
    return {
      error: "Only admins can remove editors",
    };
  }

  // Validate email
  if (!data.email || typeof data.email !== "string") {
    return {
      error: "Please provide a valid email address",
    };
  }

  try {
    // Get user by email and remove custom claim
    const user = await admin.auth().getUserByEmail(data.email);

    // Get existing claims
    const customClaims = user.customClaims || {};

    // Remove editor role
    delete customClaims.editor;

    await admin.auth().setCustomUserClaims(user.uid, customClaims);

    return {
      message: `Success! ${data.email} has had editor privileges removed.`,
    };
  } catch (err) {
    console.error("Error removing editor role:", err);

    // Return specific error messages
    if (err.code === "auth/user-not-found") {
      return {
        error: `User with email ${data.email} does not exist.`,
      };
    }

    return {
      error: `Error: ${err.message}`,
    };
  }
});
