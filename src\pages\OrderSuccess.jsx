import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import styled from "styled-components";
import { motion } from "framer-motion";
import { colors, spacing, typography, breakpoints } from "../styles";
import { useTheme } from "../context/ThemeContext";
import { FaCheckCircle, FaShoppingBag, FaHome, FaArrowRight } from "react-icons/fa";
import { formatCurrency, formatDate } from "../utils/formatters";
import { doc, getDoc } from "firebase/firestore";
import { db } from "../firebase/config";

const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: ${spacing.xl};
  
  @media (max-width: ${breakpoints.md}) {
    padding: ${spacing.lg};
  }
  
  @media (max-width: ${breakpoints.sm}) {
    padding: ${spacing.md};
  }
`;

const SuccessCard = styled(motion.div)`
  background-color: ${props => props.$darkMode ? colors.neutral.black : colors.neutral.white};
  border-radius: 12px;
  padding: ${spacing.xl};
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid ${props => props.$darkMode ? colors.neutral.dark : colors.neutral.lighter};
  
  @media (max-width: ${breakpoints.sm}) {
    padding: ${spacing.lg};
  }
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: ${spacing.xl};
`;

const Title = styled.h1`
  color: ${props => props.$darkMode ? colors.neutral.white : colors.neutral.darker};
  margin-top: ${spacing.md};
  margin-bottom: ${spacing.xs};
  font-size: ${typography.fontSize.xl};
  
  @media (max-width: ${breakpoints.sm}) {
    font-size: ${typography.fontSize.lg};
  }
`;

const Subtitle = styled.p`
  color: ${props => props.$darkMode ? colors.neutral.light : colors.neutral.gray};
  font-size: ${typography.fontSize.md};
  margin-bottom: ${spacing.lg};
  
  @media (max-width: ${breakpoints.sm}) {
    font-size: ${typography.fontSize.sm};
  }
`;

const OrderDetails = styled.div`
  margin-bottom: ${spacing.xl};
`;

const DetailRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: ${spacing.md};
  padding-bottom: ${spacing.sm};
  border-bottom: 1px solid ${props => props.$darkMode ? colors.neutral.dark : colors.neutral.lighter};
  
  &:last-child {
    border-bottom: none;
  }
`;

const DetailLabel = styled.div`
  color: ${props => props.$darkMode ? colors.neutral.light : colors.neutral.gray};
  font-size: ${typography.fontSize.sm};
`;

const DetailValue = styled.div`
  color: ${props => props.$darkMode ? colors.neutral.white : colors.neutral.darker};
  font-weight: ${typography.fontWeight.medium};
  text-align: right;
`;

const ItemsContainer = styled.div`
  margin-bottom: ${spacing.xl};
`;

const ItemRow = styled.div`
  display: flex;
  justify-content: space-between;
  padding: ${spacing.sm} 0;
  border-bottom: 1px solid ${props => props.$darkMode ? colors.neutral.dark : colors.neutral.lighter};
  
  &:last-child {
    border-bottom: none;
  }
`;

const ItemInfo = styled.div`
  display: flex;
  align-items: center;
`;

const ItemImage = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: ${spacing.sm};
  background-color: ${props => props.$darkMode ? colors.neutral.dark : colors.neutral.lighter};
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const ItemDetails = styled.div``;

const ItemName = styled.div`
  color: ${props => props.$darkMode ? colors.neutral.white : colors.neutral.darker};
  font-weight: ${typography.fontWeight.medium};
  margin-bottom: 4px;
`;

const ItemMeta = styled.div`
  color: ${props => props.$darkMode ? colors.neutral.light : colors.neutral.gray};
  font-size: ${typography.fontSize.sm};
`;

const ItemPrice = styled.div`
  color: ${props => props.$darkMode ? colors.neutral.white : colors.neutral.darker};
  font-weight: ${typography.fontWeight.medium};
`;

const TotalSection = styled.div`
  margin-top: ${spacing.lg};
  padding-top: ${spacing.md};
  border-top: 2px solid ${props => props.$darkMode ? colors.neutral.dark : colors.neutral.lighter};
`;

const TotalRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: ${spacing.sm};
  
  &:last-child {
    margin-top: ${spacing.md};
    padding-top: ${spacing.sm};
    border-top: 1px solid ${props => props.$darkMode ? colors.neutral.dark : colors.neutral.lighter};
    font-weight: ${typography.fontWeight.bold};
    font-size: ${typography.fontSize.md};
  }
`;

const TotalLabel = styled.div`
  color: ${props => props.$darkMode ? colors.neutral.light : colors.neutral.gray};
`;

const TotalValue = styled.div`
  color: ${props => props.$darkMode ? colors.neutral.white : colors.neutral.darker};
  text-align: right;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: ${spacing.xl};
  
  @media (max-width: ${breakpoints.sm}) {
    flex-direction: column;
    gap: ${spacing.md};
  }
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${spacing.sm};
  padding: ${spacing.sm} ${spacing.lg};
  background-color: ${props => props.primary 
    ? colors.primary.main 
    : props.$darkMode ? colors.neutral.dark : colors.neutral.white};
  color: ${props => props.primary 
    ? colors.neutral.white 
    : props.$darkMode ? colors.neutral.white : colors.neutral.dark};
  border: 1px solid ${props => props.primary 
    ? colors.primary.main 
    : props.$darkMode ? colors.neutral.dark : colors.neutral.light};
  border-radius: 8px;
  font-size: ${typography.fontSize.md};
  font-weight: ${typography.fontWeight.medium};
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: ${props => props.primary 
      ? colors.primary.dark 
      : props.$darkMode ? colors.neutral.darker : colors.neutral.lighter};
  }
  
  @media (max-width: ${breakpoints.sm}) {
    width: 100%;
  }
`;

const LoadingState = styled.div`
  text-align: center;
  padding: ${spacing.xl} 0;
  color: ${props => props.$darkMode ? colors.neutral.light : colors.neutral.gray};
`;

const ErrorState = styled.div`
  text-align: center;
  padding: ${spacing.xl} 0;
  color: ${colors.error.main};
`;

const OrderSuccess = () => {
  const { darkMode } = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    const fetchOrderDetails = async () => {
      try {
        // Get orderId from URL query params
        const params = new URLSearchParams(location.search);
        const orderId = params.get('orderId');
        
        if (!orderId) {
          setError("Order ID not found. Please check your order history.");
          setLoading(false);
          return;
        }
        
        // Fetch order from Firestore
        const orderDoc = await getDoc(doc(db, "orders", orderId));
        
        if (!orderDoc.exists()) {
          setError("Order not found. Please check your order history.");
          setLoading(false);
          return;
        }
        
        setOrder({
          id: orderDoc.id,
          ...orderDoc.data(),
          createdAt: orderDoc.data().createdAt?.toDate().toISOString()
        });
        
      } catch (err) {
        console.error("Error fetching order:", err);
        setError("Failed to load order details. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    
    fetchOrderDetails();
  }, [location.search]);
  
  const handleContinueShopping = () => {
    navigate('/shop');
  };
  
  const handleViewOrders = () => {
    navigate('/account', { state: { activeSection: 'orders' } });
  };
  
  const handleGoHome = () => {
    navigate('/');
  };
  
  if (loading) {
    return (
      <Container>
        <LoadingState $darkMode={darkMode}>
          <p>Loading your order details...</p>
        </LoadingState>
      </Container>
    );
  }
  
  if (error) {
    return (
      <Container>
        <ErrorState>
          <p>{error}</p>
          <Button 
            $darkMode={darkMode} 
            primary 
            onClick={handleGoHome}
            style={{ margin: '0 auto', marginTop: spacing.lg, display: 'flex' }}
          >
            <FaHome /> Return Home
          </Button>
        </ErrorState>
      </Container>
    );
  }
  
  return (
    <Container>
      <SuccessCard 
        $darkMode={darkMode}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Header>
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
          >
            <FaCheckCircle size={60} color={colors.success.main} />
          </motion.div>
          <Title $darkMode={darkMode}>Order Confirmed!</Title>
          <Subtitle $darkMode={darkMode}>
            Thank you for your purchase. Your order has been received and is being processed.
          </Subtitle>
        </Header>
        
        <OrderDetails>
          <DetailRow $darkMode={darkMode}>
            <DetailLabel $darkMode={darkMode}>Order Number</DetailLabel>
            <DetailValue $darkMode={darkMode}>{order.id}</DetailValue>
          </DetailRow>
          <DetailRow $darkMode={darkMode}>
            <DetailLabel $darkMode={darkMode}>Date</DetailLabel>
            <DetailValue $darkMode={darkMode}>{formatDate(order.createdAt)}</DetailValue>
          </DetailRow>
          <DetailRow $darkMode={darkMode}>
            <DetailLabel $darkMode={darkMode}>Status</DetailLabel>
            <DetailValue $darkMode={darkMode}>{order.status}</DetailValue>
          </DetailRow>
          <DetailRow $darkMode={darkMode}>
            <DetailLabel $darkMode={darkMode}>Payment Method</DetailLabel>
            <DetailValue $darkMode={darkMode}>
              {order.payment?.method === 'stripe' ? 'Credit Card' : order.payment?.method}
            </DetailValue>
          </DetailRow>
        </OrderDetails>
        
        <h3 style={{ 
          marginBottom: spacing.md, 
          color: darkMode ? colors.neutral.white : colors.neutral.darker 
        }}>
          Order Summary
        </h3>
        
        <ItemsContainer>
          {order.items?.map((item, index) => (
            <ItemRow key={index} $darkMode={darkMode}>
              <ItemInfo>
                <ItemImage $darkMode={darkMode}>
                  {item.imageUrl && <img src={item.imageUrl} alt={item.name} />}
                </ItemImage>
                <ItemDetails>
                  <ItemName $darkMode={darkMode}>{item.name}</ItemName>
                  <ItemMeta $darkMode={darkMode}>Qty: {item.quantity}</ItemMeta>
                </ItemDetails>
              </ItemInfo>
              <ItemPrice $darkMode={darkMode}>
                {formatCurrency(item.price * item.quantity)}
              </ItemPrice>
            </ItemRow>
          ))}
        </ItemsContainer>
        
        <TotalSection $darkMode={darkMode}>
          <TotalRow $darkMode={darkMode}>
            <TotalLabel $darkMode={darkMode}>Subtotal</TotalLabel>
            <TotalValue $darkMode={darkMode}>{formatCurrency(order.subtotal)}</TotalValue>
          </TotalRow>
          <TotalRow $darkMode={darkMode}>
            <TotalLabel $darkMode={darkMode}>Shipping</TotalLabel>
            <TotalValue $darkMode={darkMode}>{formatCurrency(order.shipping)}</TotalValue>
          </TotalRow>
          <TotalRow $darkMode={darkMode}>
            <TotalLabel $darkMode={darkMode}>Tax</TotalLabel>
            <TotalValue $darkMode={darkMode}>{formatCurrency(order.tax)}</TotalValue>
          </TotalRow>
          <TotalRow $darkMode={darkMode}>
            <TotalLabel $darkMode={darkMode}>Total</TotalLabel>
            <TotalValue $darkMode={darkMode}>{formatCurrency(order.total)}</TotalValue>
          </TotalRow>
        </TotalSection>
        
        <ButtonContainer>
          <Button $darkMode={darkMode} onClick={handleGoHome}>
            <FaHome /> Return Home
          </Button>
          <Button $darkMode={darkMode} onClick={handleViewOrders}>
            <FaShoppingBag /> View Orders
          </Button>
          <Button $darkMode={darkMode} primary onClick={handleContinueShopping}>
            Continue Shopping <FaArrowRight />
          </Button>
        </ButtonContainer>
      </SuccessCard>
    </Container>
  );
};

export default OrderSuccess;
